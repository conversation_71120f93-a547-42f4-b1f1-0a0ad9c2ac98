"""Add member_count to bands table

Revision ID: 5f77853bebb5
Revises: 
Create Date: 2025-06-28 12:19:33.532661

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5f77853bebb5'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bands', schema=None) as batch_op:
        batch_op.add_column(sa.Column('member_count', sa.Integer(), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('bands', schema=None) as batch_op:
        batch_op.drop_column('member_count')

    # ### end Alembic commands ###
