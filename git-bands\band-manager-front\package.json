{"name": "band-manager-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "check-components": "node scripts/check-components.js", "lint": "eslint . --ext .vue,.ts,.vue"}, "dependencies": {"axios": "^1.5.0", "pinia": "^2.3.1", "vue": "^3.5.13", "vue-datepicker-next": "^1.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/node": "^24.0.4", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-vue": "^4.3.0", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "sass": "^1.89.2", "typescript": "^5.2.2", "vite": "^4.4.11", "vue-tsc": "^1.8.8"}}