// src/assets/scss/_animations.scss
// Musician风格动画效果

@use 'variables' as *;

// 发光动画
@keyframes glow {
  0% {
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px $primary-color, 0 0 20px $primary-color;
  }
  100% {
    text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px $primary-color, 0 0 40px $primary-color;
  }
}

// 慢脉冲动画
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// 渐入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 缩放悬停动画
@keyframes scaleHover {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

// 工具类
.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-pulse-slow {
  animation: pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

// 文字渐变效果
.text-gradient {
  background: $gradient-primary;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

// 悬停效果
.hover-scale {
  transition: transform $transition-normal ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-glow {
  transition: box-shadow $transition-normal ease;
  
  &:hover {
    box-shadow: 0 0 20px $shadow-primary;
  }
}

// 毛玻璃效果
.backdrop-blur {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

// 背景噪点
.bg-noise {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGZpbHRlcj0idXJsKCNhKSIgb3BhY2l0eT0iLjA1IiBkPSJNMCAwaDMwMHYzMDBIMHoiLz48L3N2Zz4=');
}

// 滚动条隐藏
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 按钮样式
.btn-primary {
  background: $primary-color;
  color: white;
  border: none;
  border-radius: 9999px;
  padding: 12px 32px;
  font-weight: $font-weight-semibold;
  transition: all $transition-normal ease;
  cursor: pointer;
  
  &:hover {
    background: darken($primary-color, 10%);
    transform: scale(1.05);
    box-shadow: 0 10px 25px $shadow-primary;
  }
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid $secondary-color;
  border-radius: 9999px;
  padding: 10px 30px;
  font-weight: $font-weight-semibold;
  transition: all $transition-normal ease;
  cursor: pointer;
  
  &:hover {
    background: rgba($secondary-color, 0.1);
    transform: scale(1.05);
    box-shadow: 0 10px 25px $shadow-secondary;
  }
}

// 卡片样式
.card-dark {
  background: $darkgray-bg;
  border: 1px solid $border-color;
  border-radius: 12px;
  transition: all $transition-normal ease;
  
  &:hover {
    border-color: $border-primary;
    transform: scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }
}

// 输入框样式
.input-dark {
  background: rgba($lightgray-bg, 0.3);
  border: 1px solid $border-color;
  border-radius: 8px;
  color: $text-color;
  padding: 12px 16px;
  transition: all $transition-normal ease;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
  
  &::placeholder {
    color: $text-gray-400;
  }
}
