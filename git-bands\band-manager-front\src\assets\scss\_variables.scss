// src/assets/scss/_variables.scss
// Musician风格颜色系统
$primary-color: #ff2a6d;        // 霓虹粉
$secondary-color: #05d9e8;      // 霓虹蓝
$dark-bg: #121212;              // 深色背景
$darkgray-bg: #1e1e1e;          // 深灰色
$lightgray-bg: #2d2d2d;         // 浅灰色

// 文字颜色
$text-color: #ffffff;           // 主文字颜色
$text-gray: #d1d5db;            // 灰色文字
$text-gray-400: #9ca3af;        // 更浅的灰色文字

// 字体系统
$font-family-sans: 'Inter', 'Helvetica Neue', Arial, sans-serif;
$font-family-display: 'Montserrat', 'Helvetica Neue', Arial, sans-serif;

// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;

// 边框和阴影
$border-color: rgba(45, 45, 45, 0.5);
$border-primary: rgba(255, 42, 109, 0.2);
$border-secondary: rgba(5, 217, 232, 0.2);

// 阴影
$shadow-primary: rgba(255, 42, 109, 0.2);
$shadow-secondary: rgba(5, 217, 232, 0.2);

// 渐变
$gradient-primary: linear-gradient(135deg, #{$primary-color}, #{$secondary-color});
$gradient-bg: linear-gradient(135deg, #{$dark-bg} 0%, #{$darkgray-bg} 100%);

// 动画时长
$transition-fast: 0.2s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;