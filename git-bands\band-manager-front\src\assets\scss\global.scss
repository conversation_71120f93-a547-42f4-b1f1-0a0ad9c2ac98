// src/assets/scss/global.scss
@use 'variables' as *;
@use 'animations' as *;

// 全局重置和基础样式
* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  font-family: $font-family-sans;
  color: $text-color;
  background: $dark-bg;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  line-height: 1.6;

  // 添加背景噪点效果
  &.bg-noise {
    @extend .bg-noise;
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  font-family: $font-family-display;
  font-weight: $font-weight-bold;
  line-height: 1.2;
  margin: 0 0 1rem 0;
}

h1 {
  font-size: clamp(2.5rem, 10vw, 5rem);
  font-weight: $font-weight-extrabold;
  letter-spacing: -0.02em;
}

h2 {
  font-size: clamp(2rem, 6vw, 3rem);
  font-weight: $font-weight-bold;
  letter-spacing: -0.01em;
}

h3 {
  font-size: 1.5rem;
  font-weight: $font-weight-semibold;
}

// 链接样式
a {
  color: $primary-color;
  text-decoration: none;
  transition: color $transition-normal ease;

  &:hover {
    color: lighten($primary-color, 10%);
  }
}

// 按钮基础样式
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all $transition-normal ease;
}

// 输入框基础样式
input, textarea, select {
  font-family: inherit;
  outline: none;
}

@media (max-width: 1200px) {
  .band-showcase {
    .showcase-container {
      width: 95%;
      
      .band-card {
        flex: 0 0 45%; /* 平板设备显示两个 */
      }
    }
  }
}

@media (max-width: 768px) {
  .nav-header {
    .nav-boxes {
      flex-wrap: wrap;
      
      .nav-box {
        padding: 8px 15px;
        min-width: 100px;
      }
    }
  }
  
  .band-showcase {
    height: 60vh;
    margin-top: 80px;
    
    .nav-arrow {
      width: 40px;
      height: 40px;
      top: 40%;
      
      &.prev {
        left: 10px;
      }
      
      &.next {
        right: 10px;
      }
    }
    
    .showcase-container {
      width: 95%;
      
      .slide-group {
        gap: 20px;
      }
      
      .band-card {
        flex: 0 0 100%; /* 移动设备显示一个 */
      }
    }
  }
}

.band-management {
  background-color: #111;
  color: white;
  min-height: 100vh;
  padding: 20px;
  position: relative;
  overflow-y: auto;
}